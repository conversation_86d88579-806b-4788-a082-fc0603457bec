<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终对齐测试</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/timeline.css">
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>
    <style>
        body {
            padding: 0.5rem;
            min-height: 100vh;
        }
        .test-container {
            max-width: 100%;
            margin: 0 auto;
        }
        .controls {
            position: fixed;
            top: 5px;
            left: 5px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 8px;
            border-radius: 5px;
            font-size: 11px;
            z-index: 1000;
            max-width: 200px;
        }
        .control-button {
            background: var(--first-color);
            color: white;
            border: none;
            padding: 3px 8px;
            margin: 1px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
        }
        .theme-toggle {
            position: fixed;
            top: 5px;
            right: 5px;
            background: var(--first-color);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 11px;
            z-index: 1000;
        }
        .info {
            position: fixed;
            bottom: 5px;
            left: 5px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 8px;
            border-radius: 3px;
            font-size: 10px;
            z-index: 1000;
        }
        
        /* 测试宽度类 */
        .w320 { max-width: 320px; margin: 0 auto; border: 1px solid red; }
        .w375 { max-width: 375px; margin: 0 auto; border: 1px solid orange; }
        .w414 { max-width: 414px; margin: 0 auto; border: 1px solid yellow; }
        .w480 { max-width: 480px; margin: 0 auto; border: 1px solid green; }
        
        /* 调试模式 */
        .debug .detail-label {
            background: rgba(255,0,0,0.1);
            border: 1px solid red;
        }
        .debug .detail-value {
            background: rgba(0,0,255,0.1);
            border: 1px solid blue;
        }
    </style>
</head>
<body>
    <div class="controls">
        <div>测试宽度:</div>
        <button class="control-button" onclick="setWidth('320')">320</button>
        <button class="control-button" onclick="setWidth('375')">375</button>
        <button class="control-button" onclick="setWidth('414')">414</button>
        <button class="control-button" onclick="setWidth('480')">480</button>
        <button class="control-button" onclick="setWidth('full')">全宽</button>
        <br>
        <button class="control-button" onclick="toggleDebug()">调试模式</button>
    </div>
    
    <button class="theme-toggle" onclick="toggleTheme()">切换主题</button>
    <div class="info" id="info">宽度: 全宽 | 主题: 暗色</div>
    
    <div class="test-container" id="container">
        <h2 style="color: var(--title-color); text-align: center; margin: 1rem 0;">对齐测试</h2>
        
        <div class="about__expandable">
            <div class="expandable__item active">
                <div class="expandable__header">
                    <h3 class="expandable__title">基地信息测试</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content">
                    <div class="expandable__body">
                        <div class="timeline-container">
                            <div class="timeline-item">
                                <div class="timeline-date">
                                    <span>2025.07.20</span> 基地信息
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-details">
                                        <!-- 测试不同长度的标签 -->
                                        <div class="detail-row">
                                            <span class="detail-label">短标签：</span>
                                            <span class="detail-value">测试内容</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="detail-label">种源信息：</span>
                                            <span class="detail-value">睡莲科植物芡Euryale ferox Salisb.的干燥成熟种仁</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="detail-label">环境信息：</span>
                                            <span class="detail-value">生在池塘、湖沼中</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="detail-label">田间管理：</span>
                                            <span class="detail-value">4月份种植，种子育苗，复合肥、腐熟有机肥、尿素</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="detail-label">采收信息：</span>
                                            <span class="detail-value">11月份采收，干燥成熟种仁、人工</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="detail-label">产地初加工：</span>
                                            <span class="detail-value">秋末冬初采收成熟果实，除去果皮，取出种子，洗净，再除去硬壳（外种皮），晒干</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="detail-label">生产许可证：</span>
                                            <span class="detail-value">皖20160093</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="detail-label">执行标准：</span>
                                            <span class="detail-value">《中国药典》2020版一部及四部</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentWidth = 'full';
        let currentTheme = 'dark';
        let debugMode = false;
        
        function setWidth(width) {
            const container = document.getElementById('container');
            container.className = 'test-container';
            
            if (width !== 'full') {
                container.classList.add(`w${width}`);
                currentWidth = `${width}px`;
            } else {
                currentWidth = '全宽';
            }
            updateInfo();
        }
        
        function toggleTheme() {
            document.body.classList.toggle('light-theme');
            currentTheme = document.body.classList.contains('light-theme') ? '浅色' : '暗色';
            updateInfo();
        }
        
        function toggleDebug() {
            debugMode = !debugMode;
            document.body.classList.toggle('debug', debugMode);
        }
        
        function updateInfo() {
            document.getElementById('info').textContent = `宽度: ${currentWidth} | 主题: ${currentTheme}`;
        }
        
        // 默认暗色主题
        document.body.classList.remove('light-theme');
        updateInfo();
    </script>
</body>
</html>
