<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>暗色主题测试</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/timeline.css">
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>
    <style>
        body {
            padding: 2rem;
            min-height: 100vh;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--first-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }

        .screen-info {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }

        /* 调试边框 - 帮助查看对齐 */
        .debug .detail-label {
            border: 1px solid red;
        }

        .debug .detail-value {
            border: 1px solid blue;
        }
    </style>
</head>
<body>
    <div class="screen-info" id="screenInfo">屏幕宽度: <span id="screenWidth"></span>px</div>
    <button class="theme-toggle" onclick="toggleTheme()">切换主题</button>
    
    <div class="test-container">
        <h1 style="color: var(--title-color); text-align: center; margin-bottom: 2rem;">暗色主题优化测试</h1>
        
        <!-- 可展开项测试 -->
        <div class="about__expandable">
            <div class="expandable__item active">
                <div class="expandable__header">
                    <h3 class="expandable__title">产品信息</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content">
                    <div class="expandable__body">
                        <div class="product-info-form">
                            <div class="form-row">
                                <label class="form-label">品名</label>
                                <input type="text" class="form-input" value="芡实2" readonly>
                            </div>
                            <div class="form-row">
                                <label class="form-label">规格</label>
                                <input type="text" class="form-input" value="净制" readonly>
                            </div>
                            <div class="form-row">
                                <label class="form-label">产地</label>
                                <input type="text" class="form-input" value="广东省肇庆市" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="expandable__item active">
                <div class="expandable__header">
                    <h3 class="expandable__title">生产追溯</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content">
                    <div class="expandable__body">
                        <div class="production-trace-container">
                            <!-- 基本信息卡片 -->
                            <div class="trace-info-card">
                                <div class="form-row">
                                    <label class="form-label">生产批号</label>
                                    <input type="text" class="form-input" value="25080101A" readonly>
                                </div>
                                <div class="form-row">
                                    <label class="form-label">生产日期</label>
                                    <input type="text" class="form-input" value="2025-08-01" readonly>
                                </div>
                                <div class="form-row">
                                    <label class="form-label">保质期</label>
                                    <input type="text" class="form-input" value="2027-07-31" readonly>
                                </div>
                            </div>

                            <!-- 时间线 -->
                            <div class="timeline-container">
                                <div class="timeline-item">
                                    <div class="timeline-date">
                                        <span>2025.08.01</span> 产品信息
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-details">
                                            <div class="detail-row">
                                                <span class="detail-label">产品信息：</span>
                                                <span class="detail-value">芡实2</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">批号：</span>
                                                <span class="detail-value">25080101A</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">产地：</span>
                                                <span class="detail-value">广东省肇庆市</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">净重：</span>
                                                <span class="detail-value">119.3</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">规格：</span>
                                                <span class="detail-value">净制</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">生产日期：</span>
                                                <span class="detail-value">2025.08.01</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">保质期：</span>
                                                <span class="detail-value">24个月</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">生产许可证：</span>
                                                <span class="detail-value">皖20160093</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="timeline-item">
                                    <div class="timeline-date">
                                        <span>2025.07.20</span> 基地信息
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-details">
                                            <div class="detail-row">
                                                <span class="detail-label">种源信息：</span>
                                                <span class="detail-value">睡莲科植物芡Euryale ferox Salisb.的干燥成熟种仁</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">环境信息：</span>
                                                <span class="detail-value">生在池塘、湖沼中</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">田间管理：</span>
                                                <span class="detail-value">4月份种植，种子育苗，复合肥、腐熟有机肥、尿素</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">采收信息：</span>
                                                <span class="detail-value">11月份采收，干燥成熟种仁、人工</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">产地初加工：</span>
                                                <span class="detail-value">秋末冬初采收成熟果实，除去果皮，取出种子，洗净，再除去硬壳（外种皮），晒干</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            document.body.classList.toggle('light-theme');
            const button = document.querySelector('.theme-toggle');
            if (document.body.classList.contains('light-theme')) {
                button.textContent = '切换到暗色';
            } else {
                button.textContent = '切换到浅色';
            }
        }
        
        // 默认设置为暗色主题
        document.body.classList.remove('light-theme');

        // 显示屏幕宽度信息
        function updateScreenInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
        }

        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
