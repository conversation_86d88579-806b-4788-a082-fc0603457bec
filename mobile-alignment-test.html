<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端对齐测试</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/timeline.css">
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>
    <style>
        body {
            padding: 1rem;
            min-height: 100vh;
        }
        .test-container {
            max-width: 100%;
            margin: 0 auto;
        }
        .size-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        .size-button {
            background: var(--first-color);
            color: white;
            border: none;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }
        .theme-toggle {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--first-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            z-index: 1000;
        }
        .current-size {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 11px;
            z-index: 1000;
        }
        
        /* 测试不同宽度 */
        .width-320 { max-width: 320px; margin: 0 auto; border: 2px solid red; }
        .width-375 { max-width: 375px; margin: 0 auto; border: 2px solid orange; }
        .width-414 { max-width: 414px; margin: 0 auto; border: 2px solid yellow; }
        .width-480 { max-width: 480px; margin: 0 auto; border: 2px solid green; }
        .width-600 { max-width: 600px; margin: 0 auto; border: 2px solid blue; }
        .width-768 { max-width: 768px; margin: 0 auto; border: 2px solid purple; }
    </style>
</head>
<body>
    <div class="size-controls">
        <div>测试屏幕尺寸:</div>
        <button class="size-button" onclick="setWidth('320')">320px</button>
        <button class="size-button" onclick="setWidth('375')">375px</button>
        <button class="size-button" onclick="setWidth('414')">414px</button>
        <button class="size-button" onclick="setWidth('480')">480px</button>
        <button class="size-button" onclick="setWidth('600')">600px</button>
        <button class="size-button" onclick="setWidth('768')">768px</button>
        <button class="size-button" onclick="setWidth('full')">全宽</button>
    </div>
    
    <button class="theme-toggle" onclick="toggleTheme()">切换主题</button>
    <div class="current-size" id="currentSize">当前宽度: 全宽</div>
    
    <div class="test-container" id="testContainer">
        <h1 style="color: var(--title-color); text-align: center; margin-bottom: 2rem;">移动端对齐测试</h1>
        
        <div class="about__expandable">
            <div class="expandable__item active">
                <div class="expandable__header">
                    <h3 class="expandable__title">生产追溯</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content">
                    <div class="expandable__body">
                        <div class="production-trace-container">
                            <div class="timeline-container">
                                <div class="timeline-item">
                                    <div class="timeline-date">
                                        <span>2025.07.20</span> 基地信息
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-details">
                                            <div class="detail-row">
                                                <span class="detail-label">种源信息：</span>
                                                <span class="detail-value">睡莲科植物芡Euryale ferox Salisb.的干燥成熟种仁</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">环境信息：</span>
                                                <span class="detail-value">生在池塘、湖沼中</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">田间管理：</span>
                                                <span class="detail-value">4月份种植，种子育苗，复合肥、腐熟有机肥、尿素</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">采收信息：</span>
                                                <span class="detail-value">11月份采收，干燥成熟种仁、人工</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">产地初加工：</span>
                                                <span class="detail-value">秋末冬初采收成熟果实，除去果皮，取出种子，洗净，再除去硬壳（外种皮），晒干</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            document.body.classList.toggle('light-theme');
            const button = document.querySelector('.theme-toggle');
            if (document.body.classList.contains('light-theme')) {
                button.textContent = '暗色';
            } else {
                button.textContent = '浅色';
            }
        }
        
        function setWidth(width) {
            const container = document.getElementById('testContainer');
            const sizeDisplay = document.getElementById('currentSize');
            
            // 移除所有宽度类
            container.className = 'test-container';
            
            if (width !== 'full') {
                container.classList.add(`width-${width}`);
                sizeDisplay.textContent = `当前宽度: ${width}px`;
            } else {
                sizeDisplay.textContent = '当前宽度: 全宽';
            }
        }
        
        // 默认设置为暗色主题
        document.body.classList.remove('light-theme');
    </script>
</body>
</html>
